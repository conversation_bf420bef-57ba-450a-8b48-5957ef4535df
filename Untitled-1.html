<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 18 Weather Experience</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* iOS 18 设计系统变量 */
        :root {
            --primary-bg: #F2F2F7;
            --card-bg: rgba(255, 255, 255, 0.9);
            --text-primary: #1C1C1E;
            --text-secondary: #3A3A3C;
            --text-tertiary: #6C6C6F;
            --accent-blue: #0071E3;
            --accent-green: #34C759;
            --accent-yellow: #FFD60A;
            --accent-red: #FF3B30;
            --border-radius: 20px;
            --shadow-sm: 0 4px 6px rgba(0,0,0,0.05);
            --shadow-md: 0 8px 16px rgba(0,0,0,0.1);
            --shadow-lg: 0 16px 32px rgba(0,0,0,0.15);
            --transition: all 0.4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-bg: #1C1C1E;
                --card-bg: rgba(30, 30, 32, 0.95);
                --text-primary: #F2F2F7;
                --text-secondary: #E0E0E0;
                --text-tertiary: #A0A0A0;
                --shadow-sm: 0 4px 6px rgba(0,0,0,0.2);
                --shadow-md: 0 8px 16px rgba(0,0,0,0.3);
                --shadow-lg: 0 16px 32px rgba(0,0,0,0.4);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg), #E5E5EA);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem 1rem;
            transition: background var(--transition);
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            width: 100%;
            max-width: 1200px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            letter-spacing: -0.03em;
            margin-bottom: 0.8rem;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .header p {
            color: var(--text-tertiary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* 卡片容器 */
        .weather-container {
            display: flex;
            gap: 2.2rem;
            width: 100%;
            max-width: 1400px;
            justify-content: center;
            perspective: 2000px;
            padding: 1.5rem 0;
        }

        /* 卡片基础样式 */
        .weather-card {
            flex: 1;
            min-width: 280px;
            height: 420px;
            border-radius: var(--border-radius);
            background: var(--card-bg);
            backdrop-filter: blur(40px) saturate(180%);
            -webkit-backdrop-filter: blur(40px) saturate(180%);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            position: relative;
            transform-style: preserve-3d;
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
        }

        .weather-card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-lg);
            z-index: 10;
        }

        .weather-card.active {
            transform: rotateY(180deg);
        }

        /* 卡片内容区域 */
        .card-content {
            padding: 2.5rem 2rem;
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
            z-index: 2;
        }

        /* 天气图标区域 */
        .weather-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 180px;
            position: relative;
            margin-bottom: 1.8rem;
        }

        .weather-icon svg {
            width: 140px;
            height: 140px;
            transition: var(--transition);
            filter: drop-shadow(0 4px 12px rgba(0,0,0,0.15));
        }

        /* 天气信息 */
        .weather-info {
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .temperature {
            font-size: 4.2rem;
            font-weight: 700;
            line-height: 1;
            letter-spacing: -0.04em;
            margin: 10px 0 5px;
            color: var(--text-primary);
        }

        .feels-like {
            color: var(--text-tertiary);
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .condition {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 1.2rem;
            color: var(--text-primary);
        }

        .location {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            color: var(--text-tertiary);
            font-size: 1.15rem;
            font-weight: 500;
        }

        .location svg {
            width: 18px;
            height: 18px;
            fill: currentColor;
        }

        /* 天气特定样式 */
        .sunny .card-content { background: linear-gradient(135deg, rgba(255, 249, 196, 0.9), rgba(255, 224, 130, 0.95)); }
        .windy .card-content { background: linear-gradient(135deg, rgba(179, 229, 252, 0.9), rgba(129, 212, 250, 0.95)); }
        .rainy .card-content { background: linear-gradient(135deg, rgba(178, 235, 242, 0.9), rgba(128, 203, 196, 0.95)); }
        .snowy .card-content { background: linear-gradient(135deg, rgba(187, 222, 251, 0.9), rgba(144, 202, 249, 0.95)); }

        .sunny .weather-icon svg { stroke: #E67E00; }
        .windy .weather-icon svg { stroke: #1565C0; }
        .rainy .weather-icon svg { stroke: #0288D1; }
        .snowy .weather-icon svg { stroke: #0288D1; }

        /* 动态天气效果 */
        .sun-rays {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0.3;
            animation: sunRotate 25s linear infinite;
        }

        .sunny .sun-rays {
            display: block;
        }

        .wind-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0;
        }

        .windy .wind-lines {
            display: block;
            animation: windAppear 1.5s forwards;
        }

        .rain-drops {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0;
        }

        .rainy .rain-drops {
            display: block;
            animation: rainAppear 1.5s forwards;
        }

        .snow-flakes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0;
        }

        .snowy .snow-flakes {
            display: block;
            animation: snowAppear 1.5s forwards;
        }

        .sun-ray {
            position: absolute;
            background: rgba(255, 215, 0, 0.15);
            border-radius: 10px;
            top: 50%;
            left: 50%;
            transform-origin: 50% 100%;
        }

        .wind-line {
            position: absolute;
            height: 3px;
            background: rgba(33, 150, 243, 0.25);
            border-radius: 3px;
            animation: windFlow 4s ease-in-out infinite;
        }

        .rain-drop {
            position: absolute;
            width: 3px;
            height: 12px;
            background: rgba(74, 111, 165, 0.3);
            border-radius: 0 0 2px 2px;
            animation: rainFall 1.5s linear infinite;
        }

        .snow-flake {
            position: absolute;
            color: rgba(135, 206, 250, 0.6);
            font-size: 20px;
            animation: snowFall 8s linear infinite;
        }

        /* 动画定义 */
        @keyframes sunRotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes windAppear {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes rainAppear {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes snowAppear {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes windFlow {
            0% { transform: translateX(0) scaleX(0.8); opacity: 0.3; }
            50% { transform: translateX(20px) scaleX(1.2); opacity: 0.8; }
            100% { transform: translateX(0) scaleX(0.8); opacity: 0.3; }
        }

        @keyframes rainFall {
            0% { transform: translateY(-20px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(180px); opacity: 0; }
        }

        @keyframes snowFall {
            0% { transform: translateY(-20px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(180px) rotate(360deg); opacity: 0; }
        }

        /* 详细信息面板 */
        .details-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }

        .details-panel.active {
            opacity: 1;
            pointer-events: all;
        }

        .details-card {
            width: 90%;
            max-width: 500px;
            height: 600px;
            border-radius: var(--border-radius);
            background: var(--card-bg);
            backdrop-filter: blur(40px) saturate(180%);
            -webkit-backdrop-filter: blur(40px) saturate(180%);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.6s cubic-bezier(0.17, 0.67, 0.83, 0.67);
            display: flex;
            flex-direction: column;
        }

        .details-panel.active .details-card {
            transform: scale(1);
        }

        .details-header {
            padding: 2rem;
            background: rgba(0, 0, 0, 0.05);
            text-align: center;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .details-header h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .details-header p {
            color: var(--text-tertiary);
            font-size: 1.1rem;
        }

        .details-content {
            padding: 2.5rem 2rem;
            flex-grow: 1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.8rem;
        }

        .detail-item {
            text-align: center;
            padding: 1.5rem;
            border-radius: 14px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .detail-value {
            font-size: 2rem;
            font-weight: 700;
            margin: 8px 0 4px;
            color: var(--text-primary);
        }

        .detail-label {
            font-size: 0.95rem;
            color: var(--text-tertiary);
            font-weight: 500;
        }

        .detail-sub {
            font-size: 0.85rem;
            color: var(--accent-yellow);
            margin-top: 4px;
        }

        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            z-index: 10;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: rotate(90deg);
        }

        .close-button svg {
            width: 20px;
            height: 20px;
            stroke: var(--text-primary);
            stroke-width: 1.5;
        }

        /* 页脚 */
        .footer {
            margin-top: 3.5rem;
            text-align: center;
            color: var(--text-tertiary);
            font-size: 0.95rem;
            width: 100%;
            max-width: 1200px;
            padding: 1.5rem 0;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .footer a {
            color: var(--accent-blue);
            text-decoration: none;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .weather-container {
                flex-wrap: wrap;
            }
            .weather-card {
                min-width: 280px;
                height: 380px;
            }
            .temperature {
                font-size: 3.5rem;
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            .header p {
                font-size: 1rem;
            }
            .weather-container {
                gap: 1.5rem;
            }
            .weather-card {
                min-width: 100%;
                height: 380px;
            }
            .temperature {
                font-size: 3.2rem;
            }
            .details-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>iOS 18 Weather</h1>
        <p>Designed with Apple's latest design language for clarity, depth, and fluid motion</p>
    </div>

    <div class="weather-container">
        <!-- 晴天卡片 -->
        <div class="weather-card sunny" onclick="toggleCard(this)">
            <div class="sun-rays">
                <div class="sun-ray" style="width: 90px; height: 6px; transform: translate(-50%, -50%) rotate(0deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 70px; height: 5px; transform: translate(-50%, -50%) rotate(45deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 110px; height: 7px; transform: translate(-50%, -50%) rotate(90deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 85px; height: 6px; transform: translate(-50%, -50%) rotate(135deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 95px; height: 6px; transform: translate(-50%, -50%) rotate(180deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 75px; height: 5px; transform: translate(-50%, -50%) rotate(225deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 105px; height: 7px; transform: translate(-50%, -50%) rotate(270deg) translateY(-60px);"></div>
                <div class="sun-ray" style="width: 80px; height: 6px; transform: translate(-50%, -50%) rotate(315deg) translateY(-60px);"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5" />
                        <line x1="12" y1="1" x2="12" y2="3" />
                        <line x1="12" y1="21" x2="12" y2="23" />
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
                        <line x1="1" y1="12" x2="3" y2="12" />
                        <line x1="21" y1="12" x2="23" y2="12" />
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
                    </svg>
                </div>
                <div class="weather-info">
                    <div class="temperature">28°</div>
                    <div class="feels-like"><i class="fas fa-temperature-high"></i> 体感 30°</div>
                    <div class="condition">晴天</div>
                    <div class="location">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path d="M12 2L2 12h4v8h8v-4l8 8 4-4-8-8H12V2z"/></svg>
                        Cupertino, CA
                    </div>
        </div>

        <!-- 大风卡片 -->
        <div class="weather-card windy" onclick="toggleCard(this)">
            <div class="wind-lines">
                <div class="wind-line" style="width: 65%; top: 22%; left: 18%;"></div>
                <div class="wind-line" style="width: 55%; top: 42%; left: 22%;"></div>
                <div class="wind-line" style="width: 75%; top: 62%; left: 12%;"></div>
                <div class="wind-line" style="width: 60%; top: 82%; left: 19%;"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                        <path d="M12 5v7l7-7-3-3z"></path>
                        <path d="M2 12h20"></path>
                    </svg>
                </div>
                <div class="weather-info">
                    <div class="temperature">18°</div>
                    <div class="feels-like"><i class="fas fa-wind"></i> 风速 28 km/h</div>
                    <div class="condition">大风</div>
                    <div class="location">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path d="M12 2L2 12h4v8h8v-4l8 8 4-4-8-8H12V2z"/></svg>
                        Chicago, IL
                    </div>
                </div>
            </div>
        </div>

        <!-- 暴雨卡片 -->
        <div class="weather-card rainy" onclick="toggleCard(this)">
            <div class="rain-drops" id="rain-drops">
                <!-- Rain drops will be generated by JS -->
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                        <polyline points="12 7 12 12 15 15"></polyline>
                    </svg>
                </div>
                <div class="weather-info">
                    <div class="temperature">22°</div>
                    <div class="feels-like"><i class="fas fa-tint"></i> 降水概率 95%</div>
                    <div class="condition">暴雨</div>
                    <div class="location">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path d="M12 2L2 12h4v8h8v-4l8 8 4-4-8-8H12V2z"/></svg